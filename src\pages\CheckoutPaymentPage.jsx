import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { motion } from 'framer-motion';
import { ArrowLeft, CreditCard, Banknote, ArrowRight } from 'lucide-react';
import { useCart } from '../context/CartContext';
import { useAuth } from '../context/AuthContext';
import { toast } from 'react-toastify';
import axios from 'axios';


const CheckoutPaymentPage = () => {
  const { user } = useAuth();
  const { items, subtotal, clearCart } = useCart();
  const navigate = useNavigate();
  const location = useLocation();


  const [paymentMethod, setPaymentMethod] = useState('phonepe');
  const [isProcessingOrder, setIsProcessingOrder] = useState(false);
  const [phonePeScriptLoaded, setPhonePeScriptLoaded] = useState(false);
  const [isPageReady, setIsPageReady] = useState(false);
  const [scriptLoadError, setScriptLoadError] = useState(false);


  // ✅ Route protection and validation
  useEffect(() => {
    const currentPath = location.pathname;
    console.log('🔄 Current path on mount:', currentPath);
    
    // Ensure we're on the correct route
    if (currentPath !== '/checkout/payment') {
      console.log('❌ Invalid path detected, redirecting to checkout address');
      navigate('/checkout/address', { replace: true });
      return;
    }
    
    // Check authentication
    if (!user) {
      console.log('❌ User not authenticated, redirecting to login');
      toast.error('Please login to continue with checkout');
      navigate('/login', { replace: true });
      return;
    }


    // Validate cart
    if (!items || items.length === 0) {
      console.log('❌ Cart is empty, redirecting to cart page');
      toast.error('Your cart is empty. Please add items and try again.');
      navigate('/cart', { replace: true });
      return;
    }


    // All validations passed
    setIsPageReady(true);
    console.log('✅ Page validation successful');
  }, [location.pathname, user, items, navigate]);


  // ✅ FIXED: Improved PhonePe script loading
  useEffect(() => {
    if (!isPageReady) return;


    const loadPhonePeScript = () => {
      console.log('🔄 Starting PhonePe script loading process');


      // Check if PhonePeCheckout is already available
      if (window.PhonePeCheckout && typeof window.PhonePeCheckout.transact === 'function') {
        console.log('✅ PhonePe checkout already available');
        setPhonePeScriptLoaded(true);
        return;
      }


      // Check if script is already in DOM
      const existingScript = document.querySelector('script[src="https://mercury.phonepe.com/web/bundle/checkout.js"]');
      if (existingScript) {
        console.log('🔄 Script already exists, waiting for load...');
        
        // Wait for existing script to load
        const checkExistingScript = () => {
          if (window.PhonePeCheckout && typeof window.PhonePeCheckout.transact === 'function') {
            console.log('✅ Existing script loaded successfully');
            setPhonePeScriptLoaded(true);
          } else {
            // Keep checking every 100ms for up to 10 seconds
            setTimeout(checkExistingScript, 100);
          }
        };
        
        checkExistingScript();
        
        // Fallback timeout for existing script
        setTimeout(() => {
          if (!phonePeScriptLoaded) {
            console.log('⚠️ Existing script load timeout, removing and retrying');
            existingScript.remove();
            loadPhonePeScript();
          }
        }, 10000);
        
        return;
      }


      // Create new script element
      console.log('🔄 Creating new script element');
      const script = document.createElement('script');
      script.src = 'https://mercury.phonepe.com/web/bundle/checkout.js';
      script.async = true;
      script.defer = true;


      // Success handler
      script.onload = () => {
        console.log('🔄 Script loaded, checking for PhonePeCheckout object');
        
        // Check if PhonePeCheckout is available
        const checkPhonePeObject = () => {
          if (window.PhonePeCheckout && typeof window.PhonePeCheckout.transact === 'function') {
            console.log('✅ PhonePe checkout script loaded and ready');
            setPhonePeScriptLoaded(true);
            setScriptLoadError(false);
          } else {
            console.log('⚠️ Script loaded but PhonePeCheckout not available, retrying...');
            setTimeout(checkPhonePeObject, 100);
          }
        };


        // Start checking immediately
        checkPhonePeObject();


        // Fallback timeout
        setTimeout(() => {
          if (!phonePeScriptLoaded) {
            console.log('❌ PhonePeCheckout object not available after timeout');
            setScriptLoadError(true);
            toast.error('Failed to load payment gateway. Please refresh the page.');
          }
        }, 5000);
      };


      // Error handler
      script.onerror = (error) => {
        console.error('❌ Failed to load PhonePe script:', error);
        setScriptLoadError(true);
        toast.error('Failed to load payment gateway. Please check your internet connection and refresh.');
      };


      // Add script to document
      document.head.appendChild(script);
      console.log('🔄 Script element added to document head');


      // Global timeout fallback
      setTimeout(() => {
        if (!phonePeScriptLoaded && !scriptLoadError) {
          console.log('❌ Script loading timeout reached');
          setScriptLoadError(true);
          toast.error('Payment gateway loading timed out. Please refresh and try again.');
        }
      }, 15000);
    };


    // Start loading process
    loadPhonePeScript();


    // Cleanup function
    return () => {
      // Don't remove script on cleanup as it might be needed for other components
    };
  }, [isPageReady, phonePeScriptLoaded]);


  // Debug logging
  useEffect(() => {
    console.log('🔄 CheckoutPaymentPage state:', {
      pathname: location.pathname,
      isPageReady,
      user: !!user,
      itemsCount: items?.length || 0,
      phonePeScriptLoaded,
      scriptLoadError,
      windowPhonePeCheckout: !!window.PhonePeCheckout
    });
  }, [location.pathname, isPageReady, user, items, phonePeScriptLoaded, scriptLoadError]);


  // Calculate totals from cart items
  const calculateSubtotal = () => {
    if (subtotal && subtotal > 0) return subtotal;


    if (!items || items.length === 0) return 0;
    return items.reduce((sum, item) => {
      const effectivePrice = (item.is_sale === 1 && (item.sale_price || item.salePrice))
        ? parseFloat(item.sale_price || item.salePrice)
        : parseFloat(item.price) || 0;
      const quantity = parseInt(item.quantity) || 1;
      return sum + (effectivePrice * quantity);
    }, 0);
  };


  const calculateTotal = () => {
    return calculateSubtotal();
  };


  const handleBackToAddress = () => {
    navigate('/checkout/address');
  };


  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!paymentMethod) {
      toast.error('Please select a payment method');
      return;
    }


    if (paymentMethod === 'cod') {
      await handleCODOrder();
    } else if (paymentMethod === 'phonepe') {
      await handlePhonePePayment();
    }
  };


  // ✅ COD implementation (unchanged - working perfectly)
  const handleCODOrder = async () => {
    setIsProcessingOrder(true);


    try {
      // Get authentication token
      const getAuthToken = () => {
        try {
          const storedTokens = localStorage.getItem('wolffoxx_tokens');
          if (storedTokens) {
            const tokens = JSON.parse(storedTokens);
            return tokens.access_token || tokens.accessToken;
          }
          return null;
        } catch (error) {
          console.error('Error getting auth token:', error);
          return null;
        }
      };


      const authToken = getAuthToken();
      if (!authToken) {
        toast.error('Authentication required. Please log in again.');
        navigate('/login');
        return;
      }


      // Get user's address data
      let addressData = {};
      if (user?.addresses && user.addresses.length > 0) {
        const shippingAddress = user.addresses.find(addr => addr.type === 'shipping' && addr.is_default) ||
                               user.addresses.find(addr => addr.is_default) ||
                               user.addresses[0];
        if (shippingAddress) {
          addressData = shippingAddress;
        }
      } else if (user?.address_data) {
        addressData = user.address_data;
      }


      // Prepare order data
      const orderData = {
        customer_name: `${user?.first_name || ''} ${user?.last_name || ''}`.trim() ||
                      (addressData?.first_name && addressData?.last_name ?
                        `${addressData.first_name} ${addressData.last_name}`.trim() :
                        'Customer'),
        payment_method: 'cod',
        shipping_address: {
          line1: addressData.address_line_1 || addressData.line1 || '',
          line2: addressData.address_line_2 || addressData.line2 || '',
          city: addressData.city || '',
          state: addressData.state || '',
          postal_code: addressData.postal_code || '',
          country: addressData.country || 'India'
        },
        billing_address: {
          line1: addressData.address_line_1 || addressData.line1 || '',
          line2: addressData.address_line_2 || addressData.line2 || '',
          city: addressData.city || '',
          state: addressData.state || '',
          postal_code: addressData.postal_code || '',
          country: addressData.country || 'India'
        },
        subtotal: calculateSubtotal(),
        total_amount: calculateTotal(),
        tax_amount: 0,
        shipping_amount: 0,
        discount_amount: 0
      };


      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';


      // Create COD order
      const response = await axios.post(`${API_BASE_URL}/orders`, orderData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });


      if (response.data && response.data.success && response.data.data && response.data.data.order_number) {
        // Store order data for success page
        const orderSuccessData = {
          id: response.data.data.id,
          order_number: response.data.data.order_number,
          payment_method: 'cod',
          total_amount: calculateTotal(),
          items: items,
          shipping_address: {
            line1: addressData.address_line_1 || addressData.line1 || '',
            line2: addressData.address_line_2 || addressData.line2 || '',
            city: addressData.city || '',
            state: addressData.state || '',
            postal_code: addressData.postal_code || '',
            country: addressData.country || 'India'
          }
        };
        localStorage.setItem('lastOrderData', JSON.stringify(orderSuccessData));


        // Clear cart and redirect to success page
        clearCart();
        toast.success('Order placed successfully with Cash on Delivery!');
        navigate(`/order-success?order_id=${response.data.data.id}&order_number=${response.data.data.order_number}&payment_method=cod&total_amount=${calculateTotal()}`);
      } else {
        throw new Error('Failed to create order');
      }
    } catch (error) {
      console.error('❌ COD order creation error:', error);
      toast.error(error.response?.data?.message || 'Failed to create order. Please try again.');
    } finally {
      setIsProcessingOrder(false);
    }
  };


  // ✅ FIXED: PhonePe implementation with proper script validation
  const handlePhonePePayment = async () => {
    // Check for script loading errors
    if (scriptLoadError) {
      toast.error('Payment gateway failed to load. Please refresh the page and try again.');
      return;
    }


    // Check if script is loaded
    if (!phonePeScriptLoaded) {
      toast.error('Payment gateway is still loading. Please wait and try again.');
      return;
    }


    // Double-check PhonePe object availability
    if (!window.PhonePeCheckout || typeof window.PhonePeCheckout.transact !== 'function') {
      console.error('PhonePeCheckout object not available:', window.PhonePeCheckout);
      toast.error('Payment gateway is not properly initialized. Please refresh the page.');
      return;
    }


    setIsProcessingOrder(true);


    try {
      // Get authentication token
      const getAuthToken = () => {
        try {
          const storedTokens = localStorage.getItem('wolffoxx_tokens');
          if (storedTokens) {
            const tokens = JSON.parse(storedTokens);
            return tokens.access_token || tokens.accessToken;
          }
          return null;
        } catch (error) {
          console.error('Error getting auth token:', error);
          return null;
        }
      };


      const authToken = getAuthToken();
      if (!authToken) {
        toast.error('Authentication required. Please log in again.');
        navigate('/login');
        return;
      }


      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';


      // Create PhonePe payment request
      const orderData = {
        amount: Math.round(calculateTotal() * 100) // Convert to paise
      };


      console.log('🔄 Creating PhonePe payment request:', orderData);


      const response = await axios.post(`${API_BASE_URL}/payments/create-order`, orderData, {
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        }
      });


      console.log('🔄 PhonePe payment response:', response.data);


      if (response.data && response.data.success && response.data.data && response.data.data.payment_url) {
        // Get user's address data
        let addressData = {};
        if (user?.addresses && user.addresses.length > 0) {
          const shippingAddress = user.addresses.find(addr => addr.type === 'shipping' && addr.is_default) ||
                                 user.addresses.find(addr => addr.is_default) ||
                                 user.addresses[0];
          if (shippingAddress) {
            addressData = shippingAddress;
          }
        } else if (user?.address_data) {
          addressData = user.address_data;
        }


        // Store order data for verification
        const orderDataForVerification = {
          customer_name: `${user?.first_name || ''} ${user?.last_name || ''}`.trim() ||
                        (addressData?.first_name && addressData?.last_name ?
                          `${addressData.first_name} ${addressData.last_name}`.trim() :
                          'Customer'),
          payment_method: 'phonepe',
          shipping_address: {
            line1: addressData.address_line_1 || addressData.line1 || '',
            line2: addressData.address_line_2 || addressData.line2 || '',
            city: addressData.city || '',
            state: addressData.state || '',
            postal_code: addressData.postal_code || '',
            country: addressData.country || 'India'
          },
          billing_address: {
            line1: addressData.address_line_1 || addressData.line1 || '',
            line2: addressData.address_line_2 || addressData.line2 || '',
            city: addressData.city || '',
            state: addressData.state || '',
            postal_code: addressData.postal_code || '',
            country: addressData.country || 'India'
          },
          subtotal: calculateSubtotal(),
          total_amount: calculateTotal(),
          tax_amount: 0,
          shipping_amount: 0,
          discount_amount: 0
        };


        // Store data in localStorage for verification
        localStorage.setItem('phonepe_order_data', JSON.stringify(orderDataForVerification));
        localStorage.setItem('phonepe_merchant_transaction_id', response.data.data.merchant_transaction_id);
        localStorage.setItem('phonepe_auth_token', authToken);


        console.log('🔄 Opening PhonePe checkout with URL:', response.data.data.payment_url);


        // Launch PhonePe checkout using official script
        try {
          window.PhonePeCheckout.transact({
            tokenUrl: response.data.data.payment_url
          });


          // Set up payment completion handlers
          setupPhonePeHandlers(response.data.data.merchant_transaction_id, authToken);
        } catch (transactError) {
          console.error('❌ Error calling PhonePeCheckout.transact:', transactError);
          toast.error('Failed to open payment gateway. Please try again.');
          setIsProcessingOrder(false);
        }


      } else {
        console.error('❌ PhonePe order creation failed:', response.data);
        throw new Error(response.data?.message || 'Failed to create PhonePe payment request');
      }
    } catch (error) {
      console.error('❌ PhonePe payment error:', error);
      toast.error(error.response?.data?.message || 'Failed to initiate payment. Please try again.');
      setIsProcessingOrder(false);
    }
  };


  // ✅ Setup PhonePe payment completion handlers
  const setupPhonePeHandlers = (merchantTransactionId, authToken) => {
    console.log('🔄 Setting up PhonePe payment handlers');


    // Poll for payment status every 3 seconds
    let pollInterval;
    let pollCount = 0;
    const maxPolls = 40; // 2 minutes max


    const pollPaymentStatus = async () => {
      try {
        pollCount++;
        console.log(`🔄 Polling payment status (${pollCount}/${maxPolls}):`, merchantTransactionId);


        const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';


        const statusResponse = await axios.get(
          `${API_BASE_URL}/payments/phonepe/status/${merchantTransactionId}`,
          {
            headers: {
              'Authorization': `Bearer ${authToken}`
            }
          }
        );


        console.log('🔄 Payment status response:', statusResponse.data);


        if (statusResponse.data && statusResponse.data.success) {
          const paymentStatus = statusResponse.data.data.payment_status;
          
          if (paymentStatus === 'completed' || paymentStatus === 'success') {
            // Payment successful - verify and create order
            clearInterval(pollInterval);
            await verifyAndCreateOrder(merchantTransactionId, authToken);
          } else if (paymentStatus === 'failed' || paymentStatus === 'cancelled') {
            // Payment failed
            clearInterval(pollInterval);
            setIsProcessingOrder(false);
            toast.error('Payment was cancelled or failed. Please try again.');
          }
          // Continue polling for pending status
        }


        // Stop polling after max attempts
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          setIsProcessingOrder(false);
          toast.error('Payment verification timed out. Please check your order status.');
        }


      } catch (error) {
        console.error('❌ Error polling payment status:', error);
        // Don't stop polling on single error, continue
        if (pollCount >= maxPolls) {
          clearInterval(pollInterval);
          setIsProcessingOrder(false);
          toast.error('Payment verification failed. Please contact support.');
        }
      }
    };


    // Start polling immediately and then every 3 seconds
    pollPaymentStatus();
    pollInterval = setInterval(pollPaymentStatus, 3000);


    // Listen for when user comes back to the tab
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        console.log('🔄 Tab became visible, checking payment status');
        pollPaymentStatus();
      }
    };


    document.addEventListener('visibilitychange', handleVisibilityChange);


    // Cleanup function
    return () => {
      clearInterval(pollInterval);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  };


  // ✅ Verify payment and create order
  const verifyAndCreateOrder = async (merchantTransactionId, authToken) => {
    try {
      console.log('🔄 Verifying payment and creating order:', merchantTransactionId);


      // Get stored order data
      const orderData = JSON.parse(localStorage.getItem('phonepe_order_data') || '{}');
      
      const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1';


      // Verify payment and create order
      const verificationResponse = await axios.post(
        `${API_BASE_URL}/payments/verify`,
        {
          merchant_transaction_id: merchantTransactionId,
          order_data: orderData
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
          }
        }
      );


      console.log('🔄 Payment verification response:', verificationResponse.data);


      if (verificationResponse.data && verificationResponse.data.success) {
        // Payment verified and order created successfully
        const orderResult = verificationResponse.data.data;


        // Store order data for success page
        const orderSuccessData = {
          id: orderResult.order_id,
          order_number: orderResult.order_number,
          payment_method: 'phonepe',
          total_amount: orderResult.total_amount,
          items: items,
          shipping_address: orderData.shipping_address
        };
        localStorage.setItem('lastOrderData', JSON.stringify(orderSuccessData));


        // Clear cart and stored payment data
        clearCart();
        localStorage.removeItem('phonepe_order_data');
        localStorage.removeItem('phonepe_merchant_transaction_id');
        localStorage.removeItem('phonepe_auth_token');


        toast.success('Payment successful! Order placed successfully.');


        // Navigate to success page (same as COD)
        navigate(`/order-success?order_id=${orderResult.order_id}&order_number=${orderResult.order_number}&payment_method=phonepe&total_amount=${orderResult.total_amount}`);


      } else {
        console.error('❌ Payment verification failed:', verificationResponse.data);
        throw new Error(verificationResponse.data?.message || 'Payment verification failed');
      }


    } catch (error) {
      console.error('❌ Payment verification error:', error);
      toast.error(error.response?.data?.message || 'Payment verification failed. Please contact support.');
    } finally {
      setIsProcessingOrder(false);
    }
  };


  // ✅ FIXED: Better loading state management
  const isPhonePeReady = phonePeScriptLoaded && !scriptLoadError && window.PhonePeCheckout;
  
  // ✅ Don't render anything until page is ready
  if (!isPageReady) {
    return (
      <div className="min-h-screen bg-black text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#FF6F35] mx-auto mb-4"></div>
          <p className="text-gray-400">Loading checkout...</p>
        </div>
      </div>
    );
  }


  return (
    <div className="min-h-screen bg-black text-white">
      <div className="container mx-auto px-4 py-8 max-w-2xl">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <button
            onClick={handleBackToAddress}
            className="flex items-center gap-2 text-gray-400 hover:text-white mb-4 transition-colors"
          >
            <ArrowLeft size={20} />
            Back to Address
          </button>
          
          <div className="flex items-center gap-3 mb-2">
            <CreditCard className="text-[#FF6F35]" size={24} />
            <h1 className="text-2xl font-bold">Payment Method</h1>
          </div>
          <p className="text-gray-400">Choose your preferred payment method</p>
        </motion.div>


        {/* Order Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-[#1a1a1a] border border-gray-700 rounded-lg p-6 mb-6"
        >
          <h3 className="text-white font-semibold text-lg mb-4">Order Summary</h3>
          
          <div className="space-y-3">
            <div className="flex justify-between text-base text-[#AAAAAA]">
              <span>Subtotal ({items?.length || 0} item{(items?.length || 0) !== 1 ? 's' : ''})</span>
              <span>₹{calculateSubtotal().toFixed(2)}</span>
            </div>
            <div className="flex justify-between text-sm text-[#AAAAAA]">
              <span>Shipping</span>
              <span className="text-green-400 font-medium">Free</span>
            </div>
            <div className="border-t border-gray-700 pt-3">
              <div className="flex justify-between font-semibold text-xl">
                <span className="text-white">Total</span>
                <span className="text-[#FF6F35]">₹{calculateTotal().toFixed(2)}</span>
              </div>
            </div>
          </div>
        </motion.div>


        {/* Payment Method Selection */}
        <motion.form
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          onSubmit={handleSubmit}
          className="bg-[#1a1a1a] border border-gray-700 rounded-lg p-6"
        >
          <h3 className="text-lg font-semibold mb-6">Select Payment Method</h3>
          
          <div className="space-y-4 mb-6">
            {/* Cash on Delivery */}
            <label className={`flex items-center p-4 border rounded-lg cursor-pointer transition-all ${
              paymentMethod === 'cod' 
                ? 'border-[#FF6F35] bg-[#FF6F35]/10' 
                : 'border-gray-600 hover:border-gray-500'
            }`}>
              <input
                type="radio"
                name="paymentMethod"
                value="cod"
                checked={paymentMethod === 'cod'}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="sr-only"
              />
              <div className={`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ${
                paymentMethod === 'cod' ? 'border-[#FF6F35]' : 'border-gray-400'
              }`}>
                {paymentMethod === 'cod' && (
                  <div className="w-3 h-3 rounded-full bg-[#FF6F35]"></div>
                )}
              </div>
              <Banknote className="text-[#FF6F35] mr-3" size={24} />
              <div>
                <div className="text-white font-medium">Cash on Delivery</div>
                <div className="text-gray-400 text-sm">Pay when your order arrives</div>
              </div>
            </label>


            {/* PhonePe */}
            <label className={`flex items-center p-4 border rounded-lg cursor-pointer transition-all ${
              paymentMethod === 'phonepe'
                ? 'border-[#FF6F35] bg-[#FF6F35]/10'
                : 'border-gray-600 hover:border-gray-500'
            }`}>
              <input
                type="radio"
                name="paymentMethod"
                value="phonepe"
                checked={paymentMethod === 'phonepe'}
                onChange={(e) => setPaymentMethod(e.target.value)}
                className="sr-only"
              />
              <div className={`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ${
                paymentMethod === 'phonepe' ? 'border-[#FF6F35]' : 'border-gray-400'
              }`}>
                {paymentMethod === 'phonepe' && (
                  <div className="w-3 h-3 rounded-full bg-[#FF6F35]"></div>
                )}
              </div>
              <CreditCard className="text-[#FF6F35] mr-3" size={24} />
              <div className="flex-grow">
                <div className="text-white font-medium">
                  PhonePe Payment
                  {!isPhonePeReady && !scriptLoadError && (
                    <span className="text-yellow-400 text-xs ml-2">(Loading...)</span>
                  )}
                  {scriptLoadError && (
                    <span className="text-red-400 text-xs ml-2">(Error loading)</span>
                  )}
                  {isPhonePeReady && (
                    <span className="text-green-400 text-xs ml-2">✓ Ready</span>
                  )}
                </div>
                <div className="text-gray-400 text-sm">Pay securely with PhonePe - UPI, cards, wallets & more</div>
              </div>
            </label>
          </div>


          {/* ✅ FIXED: Better error handling in submit button */}
          <button
            type="submit"
            disabled={
              isProcessingOrder || 
              !paymentMethod || 
              (paymentMethod === 'phonepe' && !isPhonePeReady) ||
              (paymentMethod === 'phonepe' && scriptLoadError)
            }
            className="w-full bg-[#FF6F35] hover:bg-[#FF6F35]/90 text-white font-medium py-4 px-6 rounded-lg transition-all duration-300 flex items-center justify-center gap-2 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isProcessingOrder ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                {paymentMethod === 'phonepe' ? 'Processing Payment...' : 'Processing...'}
              </>
            ) : (
              <>
                <ArrowRight size={20} />
                {paymentMethod === 'cod' ? 'Place Order' : 'Proceed to Payment'}
              </>
            )}
          </button>


          {/* ✅ FIXED: Better status messages */}
          {paymentMethod === 'phonepe' && (
            <div className="mt-4 text-center text-sm">
              {!isPhonePeReady && !scriptLoadError && (
                <div className="text-yellow-400 flex items-center justify-center gap-2">
                  <div className="animate-spin w-4 h-4 border border-yellow-400 border-t-transparent rounded-full"></div>
                  Loading payment gateway...
                </div>
              )}
              {scriptLoadError && (
                <div className="text-red-400">
                  Payment gateway failed to load. Please refresh the page and try again.
                </div>
              )}
              {isPhonePeReady && (
                <div className="text-green-400">
                  ✓ Payment gateway ready
                </div>
              )}
            </div>
          )}
        </motion.form>
      </div>
    </div>
  );
};


export default CheckoutPaymentPage;
now tell me the exact issuee