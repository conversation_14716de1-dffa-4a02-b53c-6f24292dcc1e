<?php

namespace Wolffoxx\Services;

/**
 * PhonePe Payment Gateway Service - Based on Working Implementation
 */
class PhonePeService
{
    private string $clientId;
    private string $clientSecret;
    private string $clientVersion;
    private string $tokenUrl;
    private string $paymentUrl;
    private string $statusUrl;
    private string $environment;

    public function __construct()
    {
        try {
            // ✅ Use the same credentials as your working code
            $this->clientId = $_ENV['PHONEPE_CLIENT_ID'] ?? 'SU2505222105162650976562';
            $this->clientSecret = $_ENV['PHONEPE_CLIENT_SECRET'] ?? '09241490-50b2-4570-83ce-22fe4a1feaa3';
            $this->clientVersion = $_ENV['PHONEPE_CLIENT_VERSION'] ?? '1';
            $this->environment = $_ENV['PHONEPE_ENVIRONMENT'] ?? 'SANDBOX';

            // ✅ Use exact URLs from your working code
            if ($this->environment === 'PRODUCTION') {
                $this->tokenUrl = 'https://api.phonepe.com/apis/identity-manager/v1/oauth/token';
                $this->paymentUrl = 'https://api.phonepe.com/apis/pg/checkout/v2/pay';
                $this->statusUrl = 'https://api.phonepe.com/apis/pg/checkout/v2/order/';
            } else {
                $this->tokenUrl = 'https://api-preprod.phonepe.com/apis/pg-sandbox/v1/oauth/token';
                $this->paymentUrl = 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/pay';
                $this->statusUrl = 'https://api-preprod.phonepe.com/apis/pg-sandbox/checkout/v2/order/';
            }

            error_log('PhonePe Service initialized - Environment: ' . $this->environment);
        } catch (\Exception $e) {
            error_log('PhonePe Service initialization failed: ' . $e->getMessage());
            throw $e;
        }
    }

    /**
     * ✅ Get OAuth access token - exactly like working code
     */
    private function getToken(): ?string
    {
        try {
            $postData = http_build_query([
                'client_id' => $this->clientId,
                'client_version' => $this->clientVersion,
                'client_secret' => $this->clientSecret,
                'grant_type' => 'client_credentials'
            ]);

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $this->tokenUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $postData,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/x-www-form-urlencoded'
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);

            if ($error) {
                error_log("CURL ERROR: $error");
                return null;
            }

            if ($httpCode !== 200) {
                error_log("Token request failed with HTTP code: $httpCode, Response: $response");
                return null;
            }

            $responseData = json_decode($response, true);
            return $responseData['access_token'] ?? null;

        } catch (\Exception $e) {
            error_log('Token generation exception: ' . $e->getMessage());
            return null;
        }
    }

    /**
     * ✅ Create payment request - exactly like working code
     */
    public function createPaymentRequest(array $paymentData): ?array
    {
        try {
            $token = $this->getToken();
            if (!$token) {
                throw new \Exception('Failed to get access token');
            }

            // ✅ Generate merchant order ID like working code
            $merchantOrderId = 'merch_' . date('YmdHis') . rand(1000, 9999);

            // ✅ Build payment payload exactly like working code
            $payloadData = [
                "merchantOrderId" => $merchantOrderId,
                "amount" => $paymentData['amount'], // Amount in paise
                "expireAfter" => 1200,
                "metaInfo" => [
                    "udf1" => $paymentData['user_name'] ?? 'Customer',
                    "udf2" => $paymentData['user_phone'] ?? ''
                ],
                "paymentFlow" => [
                    "type" => "PG_CHECKOUT",
                    "message" => "Payment for order",
                    "merchantUrls" => [
                        "redirectUrl" => $paymentData['redirect_url'] ?? ($_ENV['APP_URL'] . '/api/v1/payments/callback')
                    ]
                ]
            ];

            $payload = json_encode($payloadData);

            error_log('PhonePe Payment Payload: ' . $payload);

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $this->paymentUrl,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => $payload,
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Authorization: O-Bearer ' . $token  // ✅ Note the "O-Bearer" prefix like working code
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);

            error_log("PAYMENT REQUEST: HTTP Code: $httpCode, Response: $response");

            if ($error) {
                error_log("CURL ERROR: $error");
                return [
                    'success' => false,
                    'error' => $error
                ];
            }

            $responseData = json_decode($response, true);

            if (isset($responseData['redirectUrl'])) {
                return [
                    'success' => true,
                    'merchantTransactionId' => $merchantOrderId,
                    'paymentUrl' => $responseData['redirectUrl'],
                    'response' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to initiate payment',
                    'response' => $responseData
                ];
            }

        } catch (\Exception $e) {
            error_log('PhonePe Payment Request Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * ✅ Check payment status - exactly like working code
     */
    public function verifyPayment(string $merchantOrderId): ?array
    {
        try {
            $token = $this->getToken();
            if (!$token) {
                throw new \Exception('Failed to get access token');
            }

            $curl = curl_init();
            curl_setopt_array($curl, [
                CURLOPT_URL => $this->statusUrl . $merchantOrderId . '/status',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'GET',
                CURLOPT_HTTPHEADER => [
                    'Content-Type: application/json',
                    'Authorization: O-Bearer ' . $token  // ✅ Note the "O-Bearer" prefix
                ],
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false
            ]);

            $response = curl_exec($curl);
            $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            $error = curl_error($curl);
            curl_close($curl);

            error_log("STATUS CHECK: HTTP Code: $httpCode, Response: $response");

            if ($error) {
                error_log("CURL ERROR: $error");
                return [
                    'success' => false,
                    'error' => $error
                ];
            }

            $responseData = json_decode($response, true);

            if (isset($responseData['orderId'])) {
                return [
                    'success' => true,
                    'status' => $responseData['state'] ?? 'UNKNOWN',
                    'orderId' => $responseData['orderId'],
                    'amount' => $responseData['amount'] ?? null,
                    'errorCode' => $responseData['errorCode'] ?? null,
                    'metaInfo' => $responseData['metaInfo'] ?? [],
                    'response' => $responseData
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Invalid response from PhonePe',
                    'response' => $responseData
                ];
            }

        } catch (\Exception $e) {
            error_log('PhonePe Status Check Exception: ' . $e->getMessage());
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Get merchant configuration
     */
    public function getMerchantConfig(): array
    {
        return [
            'clientId' => $this->clientId,
            'environment' => $this->environment,
            'tokenUrl' => $this->tokenUrl,
            'paymentUrl' => $this->paymentUrl
        ];
    }
}
