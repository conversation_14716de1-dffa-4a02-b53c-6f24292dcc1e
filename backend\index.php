<?php

/**
 * Wolffoxx Ecommerce API Entry Point
 *
 * Main application bootstrap file that handles all incoming requests
 * and routes them to appropriate controllers.
 */

// Set error reporting for development
if ($_ENV['APP_ENV'] ?? 'production' === 'development') {
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
} else {
    error_reporting(0);
    ini_set('display_errors', 0);
}

// Set timezone
date_default_timezone_set('UTC');

// Autoload dependencies
require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
$dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
$dotenv->load();

// Import required classes
use Wolffoxx\Config\Database;
use Wolffoxx\Config\CORS;
use Wolffoxx\Config\JWTConfig;
use Wolffoxx\Utils\Logger;
use Wolffoxx\Middleware\AuthMiddleware;
use Wolffoxx\Middleware\RateLimitMiddleware;
use Wolffoxx\Utils\Router;
use Wolffoxx\Utils\Response;

try {
    // Initialize logger
    $logger = new Logger('app');
    $logger->info('Application starting', [
        'method' => $_SERVER['REQUEST_METHOD'] ?? 'UNKNOWN',
        'uri' => $_SERVER['REQUEST_URI'] ?? 'UNKNOWN',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'UNKNOWN'
    ]);

    // Initialize configurations
    Database::init();
    CORS::init();
    JWTConfig::init();

    // Handle CORS
    CORS::handle();

    // Validate CORS request
    if (!CORS::validateRequest()) {
        CORS::sendErrorResponse('CORS policy violation');
        exit;
    }

    // Apply rate limiting
    $rateLimiter = new RateLimitMiddleware();
    if (!$rateLimiter->handle()) {
        Response::error('Rate limit exceeded', 429);
        exit;
    }

    // Initialize router
    $router = new Router();

    // Get request method and URI
    $method = $_SERVER['REQUEST_METHOD'] ?? 'GET';
    $uri = parse_url($_SERVER['REQUEST_URI'] ?? '/', PHP_URL_PATH);

    // Remove API prefix if present
    $apiPrefix = $_ENV['API_PREFIX'] ?? '/api/v1';
    if (strpos($uri, $apiPrefix) === 0) {
        $uri = substr($uri, strlen($apiPrefix));
    }

    // Ensure URI starts with /
    if (!str_starts_with($uri, '/')) {
        $uri = '/' . $uri;
    }

    $logger->debug('Processing request', [
        'method' => $method,
        'uri' => $uri,
        'original_uri' => $_SERVER['REQUEST_URI'] ?? '/'
    ]);

    // Define API routes

    // Root route - API info
    $router->get('/', function() {
        Response::success([
            'message' => 'Wolffoxx Ecommerce API',
            'version' => '1.0.0',
            'status' => 'running',
            'timestamp' => date('c'),
            'environment' => $_ENV['APP_ENV'] ?? 'production',
            'endpoints' => [
                'products' => '/api/v1/products',
                'categories' => '/api/v1/categories',
                'auth' => '/api/v1/auth/otp/send',
                'cart' => '/api/v1/cart',
                'orders' => '/api/v1/orders',
                'payments' => '/api/v1/payments/create-order',
                'health' => '/api/v1/health'
            ]
        ]);
    });
    // ✅ Add this temporary debug handler at the top of index.php
if ($_SERVER['REQUEST_METHOD'] === 'POST' && 
    strpos($_SERVER['REQUEST_URI'], '/api/v1/payments/create-order') !== false) {
    
    error_log('=== DIRECT PAYMENT ROUTE DEBUG ===');
    error_log('Method: ' . $_SERVER['REQUEST_METHOD']);
    error_log('URI: ' . $_SERVER['REQUEST_URI']);
    error_log('Content-Type: ' . ($_SERVER['CONTENT_TYPE'] ?? 'not set'));
    
    // Enable CORS for this test
    header('Access-Control-Allow-Origin: http://localhost:5173');
    header('Access-Control-Allow-Methods: POST, OPTIONS');
    header('Access-Control-Allow-Headers: Content-Type, Authorization');
    header('Content-Type: application/json');
    
    if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
        http_response_code(200);
        exit;
    }
    
    try {
        // Try to instantiate the controller
        require_once __DIR__ . '/vendor/autoload.php';
        
        // Load environment
        $dotenv = Dotenv\Dotenv::createImmutable(__DIR__);
        $dotenv->load();
        
        
        Database::init();
                
        echo json_encode([
            'success' => true,
            'message' => 'Direct route test successful',
            'controller_loaded' => true,
            'timestamp' => date('c')
        ]);
        exit;
        
    } catch (Exception $e) {
        error_log('Direct route error: ' . $e->getMessage());
        
        echo json_encode([
            'success' => false,
            'error' => 'Direct route test failed: ' . $e->getMessage(),
            'timestamp' => date('c')
        ]);
        exit;
    }
}


    // Health check
    $router->get('/health', function() {
        Response::success([
            'status' => 'healthy',
            'timestamp' => date('c'),
            'version' => '1.0.0',
            'environment' => $_ENV['APP_ENV'] ?? 'production'
        ]);
    });

    // Email-based Authentication routes (legacy)
    $router->post('/auth/register', 'Wolffoxx\\Controllers\\AuthController@register');
    $router->post('/auth/login', 'Wolffoxx\\Controllers\\AuthController@login');
    $router->post('/auth/logout', 'Wolffoxx\\Controllers\\AuthController@logout');
    $router->post('/auth/refresh', 'Wolffoxx\\Controllers\\AuthController@refresh');
    $router->post('/auth/forgot-password', 'Wolffoxx\\Controllers\\AuthController@forgotPassword');
    $router->post('/auth/reset-password', 'Wolffoxx\\Controllers\\AuthController@resetPassword');
    $router->get('/auth/verify-email/{token}', 'Wolffoxx\\Controllers\\AuthController@verifyEmail');

    // OTP-based Authentication routes (primary)
    $router->post('/auth/otp/send', 'Wolffoxx\\Controllers\\OTPAuthController@sendOTP');
    $router->post('/auth/otp/verify', 'Wolffoxx\\Controllers\\OTPAuthController@verifyOTP');
    $router->post('/auth/otp/resend', 'Wolffoxx\\Controllers\\OTPAuthController@resendOTP');
    $router->put('/auth/otp/profile', 'Wolffoxx\\Controllers\\OTPAuthController@updateProfile', [AuthMiddleware::class]);

    // OTP Admin routes
    $router->get('/auth/otp/stats', 'Wolffoxx\\Controllers\\OTPAuthController@getOTPStats', [AuthMiddleware::class . ':admin']);
    $router->post('/auth/otp/cleanup', 'Wolffoxx\\Controllers\\OTPAuthController@cleanupOTPs', [AuthMiddleware::class . ':admin']);

    // User routes (protected)
    $router->group('/users', function($router) {
        $router->get('/profile', 'Wolffoxx\\Controllers\\UserController@getProfile');
        $router->put('/profile', 'Wolffoxx\\Controllers\\UserController@updateProfile');
        $router->post('/profile/image', 'Wolffoxx\\Controllers\\UserController@uploadProfileImage');
        $router->delete('/profile/image', 'Wolffoxx\\Controllers\\UserController@deleteProfileImage');
        $router->get('/addresses', 'Wolffoxx\\Controllers\\UserController@getAddresses');
        $router->post('/addresses', 'Wolffoxx\\Controllers\\UserController@createAddress');
        $router->put('/addresses/{id}', 'Wolffoxx\\Controllers\\UserController@updateAddress');
        $router->delete('/addresses/{id}', 'Wolffoxx\\Controllers\\UserController@deleteAddress');
    }, [AuthMiddleware::class]);

    // Product routes (public)
    $router->get('/products', 'Wolffoxx\\Controllers\\ProductController@index');
    $router->get('/products/search', 'Wolffoxx\\Controllers\\ProductController@search');
    $router->get('/products/category/{category}', 'Wolffoxx\\Controllers\\ProductController@getByCategory');
    $router->get('/products/{id}', 'Wolffoxx\\Controllers\\ProductController@show');
    $router->get('/categories', 'Wolffoxx\\Controllers\\ProductController@getCategories');

    // Cart routes (protected)
    $router->group('/cart', function($router) {
        $router->get('/', 'Wolffoxx\\Controllers\\CartController@getCart');
        $router->post('/items', 'Wolffoxx\\Controllers\\CartController@addItem');
        $router->put('/items/{id}', 'Wolffoxx\\Controllers\\CartController@updateItem');
        $router->delete('/items/{id}', 'Wolffoxx\\Controllers\\CartController@removeItem');
        $router->delete('/clear', 'Wolffoxx\\Controllers\\CartController@clearCart');
        $router->get('/count', 'Wolffoxx\\Controllers\\CartController@getCartCount');
    }, [AuthMiddleware::class]);

    // Wishlist routes (protected)
    $router->group('/wishlist', function($router) {
        $router->get('/', 'Wolffoxx\\Controllers\\WishlistController@index');
        $router->post('/items', 'Wolffoxx\\Controllers\\WishlistController@addItem');
        $router->delete('/items/{productId}', 'Wolffoxx\\Controllers\\WishlistController@removeItem');
        $router->put('/items/{productId}', 'Wolffoxx\\Controllers\\WishlistController@updateItem');
        $router->get('/items/{productId}/check', 'Wolffoxx\\Controllers\\WishlistController@checkItem');
        $router->get('/stats', 'Wolffoxx\\Controllers\\WishlistController@getStats');
        $router->delete('/clear', 'Wolffoxx\\Controllers\\WishlistController@clear');
    }, [AuthMiddleware::class]);

    // Outfit routes (protected)
    $router->group('/outfits', function($router) {
        $router->get('/', 'Wolffoxx\\Controllers\\OutfitController@index');
        $router->post('/', 'Wolffoxx\\Controllers\\OutfitController@create');
        $router->get('/{id}', 'Wolffoxx\\Controllers\\OutfitController@show');
        $router->put('/{id}', 'Wolffoxx\\Controllers\\OutfitController@update');
        $router->delete('/{id}', 'Wolffoxx\\Controllers\\OutfitController@delete');
        $router->post('/{id}/items', 'Wolffoxx\\Controllers\\OutfitController@addItem');
        $router->delete('/{id}/items/{itemId}', 'Wolffoxx\\Controllers\\OutfitController@removeItem');
        $router->get('/collections', 'Wolffoxx\\Controllers\\OutfitController@getCollections');
        $router->post('/collections', 'Wolffoxx\\Controllers\\OutfitController@createCollection');
        $router->post('/{id}/like', 'Wolffoxx\\Controllers\\OutfitController@toggleLike');
        $router->post('/{id}/share', 'Wolffoxx\\Controllers\\OutfitController@shareOutfit');
    }, [AuthMiddleware::class]);

    // Order routes (protected)
    $router->group('/orders', function($router) {
        $router->get('/', 'Wolffoxx\\Controllers\\OrderController@index');
        $router->post('/', 'Wolffoxx\\Controllers\\OrderController@create');
        $router->get('/{id}', 'Wolffoxx\\Controllers\\OrderController@show');
        $router->put('/{id}/status', 'Wolffoxx\\Controllers\\OrderController@updateStatus');
        $router->get('/{id}/track', 'Wolffoxx\\Controllers\\OrderController@trackOrder');
    }, [AuthMiddleware::class]);

    // ✅ Payment routes (PhonePe integration) - PROTECTED
    $router->group('/payments', function($router) {
        // PhonePe payment creation
        $router->post('/create-order', 'Wolffoxx\\Controllers\\PaymentController@createPhonePeOrder');
        
        // Payment verification after PhonePe callback
        $router->post('/verify', 'Wolffoxx\\Controllers\\PaymentController@verifyPayment');
        
        // PhonePe callback handler (for authenticated users)
        $router->post('/callback', 'Wolffoxx\\Controllers\\PaymentController@handlePhonePeCallback');
        
        // Payment failure handler
        $router->post('/failure', 'Wolffoxx\\Controllers\\PaymentController@handlePaymentFailure');
        
        // PhonePe payment status check (for polling)
        $router->get('/phonepe/status/{merchantTransactionId}', 'Wolffoxx\\Controllers\\PaymentController@checkPhonePePaymentStatus');
        
        // Generic payment status check
        $router->get('/{payment_id}/status', 'Wolffoxx\\Controllers\\PaymentController@getPaymentStatus');
    }, [AuthMiddleware::class]);

    // ✅ Public payment webhook route (PhonePe webhook - no auth required)
    $router->post('/payments/webhook', 'Wolffoxx\\Controllers\\PaymentController@handlePhonePeCallback');

    // Review routes
    $router->get('/products/{productId}/reviews', 'Wolffoxx\\Controllers\\ReviewController@getProductReviews');
    $router->get('/products/{productId}/user-review', 'Wolffoxx\\Controllers\\ReviewController@getUserProductReview', [AuthMiddleware::class]);
    $router->post('/products/{productId}/reviews', 'Wolffoxx\\Controllers\\ReviewController@createReview', [AuthMiddleware::class]);
    $router->put('/reviews/{id}', 'Wolffoxx\\Controllers\\ReviewController@updateReview', [AuthMiddleware::class]);
    $router->delete('/reviews/{id}', 'Wolffoxx\\Controllers\\ReviewController@deleteReview', [AuthMiddleware::class]);
    $router->get('/reviews/{id}/user-vote', 'Wolffoxx\\Controllers\\ReviewController@getUserVoteOnReview', [AuthMiddleware::class]);
    $router->post('/reviews/{id}/vote', 'Wolffoxx\\Controllers\\ReviewController@voteReview', [AuthMiddleware::class]);
    $router->post('/reviews/{id}/flag', 'Wolffoxx\\Controllers\\ReviewController@flagReview', [AuthMiddleware::class]);
    $router->post('/reviews/{id}/images', 'Wolffoxx\\Controllers\\ReviewController@uploadReviewImages', [AuthMiddleware::class]);
    $router->delete('/reviews/{reviewId}/images/{imageId}', 'Wolffoxx\\Controllers\\ReviewController@deleteReviewImage', [AuthMiddleware::class]);

    // Public shared content routes
    $router->get('/shared/wishlist/{token}', 'Wolffoxx\\Controllers\\SharedController@getSharedWishlist');
    $router->get('/shared/outfit/{token}', 'Wolffoxx\\Controllers\\SharedController@getSharedOutfit');

    // Email routes
    $router->post('/emails/order-confirmation', 'Wolffoxx\\Controllers\\EmailController@sendOrderConfirmation', [AuthMiddleware::class]);

    // Admin routes (protected with admin middleware)
    $router->group('/admin', function($router) {
        $router->get('/users', 'Wolffoxx\\Controllers\\AdminController@getUsers');
        $router->get('/analytics', 'Wolffoxx\\Controllers\\AdminController@getAnalytics');
        $router->post('/products', 'Wolffoxx\\Controllers\\AdminController@createProduct');
        $router->put('/products/{id}', 'Wolffoxx\\Controllers\\AdminController@updateProduct');
        $router->delete('/products/{id}', 'Wolffoxx\\Controllers\\AdminController@deleteProduct');
    }, [AuthMiddleware::class . ':admin']);

    // Route the request
    $router->dispatch($method, $uri);

} catch (Throwable $e) {
    // Log the error
    if (isset($logger)) {
        $logger->logException($e, 'Unhandled application error');
    } else {
        error_log('Application error: ' . $e->getMessage());
    }

    // Send error response
    if ($_ENV['APP_ENV'] === 'development') {
        Response::error([
            'message' => $e->getMessage(),
            'file' => $e->getFile(),
            'line' => $e->getLine(),
            'trace' => $e->getTraceAsString()
        ], 500);
    } else {
        Response::error('Internal server error', 500);
    }
}
