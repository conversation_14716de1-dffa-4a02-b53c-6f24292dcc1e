<?php

namespace <PERSON>oxx\Controllers;

use Wolffoxx\Models\Order;
use Wolffoxx\Models\Cart;
use Wolffoxx\Models\User;
use Wolffoxx\Services\NotificationService;
use Wolffoxx\Services\EmailService;
use Wolffoxx\Services\PhonePeService;
use Wolffoxx\Utils\Response;
use Wolffoxx\Middleware\AuthMiddleware;

/**
 * Payment Controller - Updated to match working PhonePe implementation
 */
class PaymentController
{
    private Order $orderModel;
    private Cart $cartModel;
    private User $userModel;
    private NotificationService $notificationService;
    private EmailService $emailService;
    private PhonePeService $phonePeService;

    public function __construct()
    {
        $this->orderModel = new Order();
        $this->cartModel = new Cart();
        $this->userModel = new User();
        $this->notificationService = new NotificationService();
        $this->emailService = new EmailService();
        $this->phonePeService = new PhonePeService();
    }

    /**
     * ✅ Create PhonePe payment request - simplified approach
     */
    public function createPhonePeOrder(): void
    {
        error_log('PaymentController::createPhonePeOrder - Method called');

        try {
            $user = AuthMiddleware::getCurrentUserData();
            if (!$user) {
                error_log('PaymentController::createPhonePeOrder - Authentication failed');
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            error_log('PaymentController::createPhonePeOrder - Input: ' . json_encode($input));

            // Validate required fields
            if (empty($input['amount'])) {
                error_log('PaymentController::createPhonePeOrder - Missing amount');
                Response::error('Amount is required', 400);
                return;
            }

            // Get user's cart to validate amount
            $cart = $this->cartModel->getUserCart($user['id']);
            if (!$cart || empty($cart['items'])) {
                error_log('PaymentController::createPhonePeOrder - Cart is empty');
                Response::error('Your cart is empty. Please add items to your cart before proceeding to payment.', 400);
                return;
            }

            // Validate amount matches cart total
            $expectedAmount = (int)($cart['total_amount'] * 100); // Convert to paise
            if ((int)$input['amount'] !== $expectedAmount) {
                error_log('PaymentController::createPhonePeOrder - Amount mismatch');
                Response::error('Amount mismatch', 400);
                return;
            }

            // ✅ Prepare payment data like working code
            $paymentData = [
                'amount' => $input['amount'], // Amount in paise
                'user_name' => trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: 'Customer',
                'user_phone' => $user['phone'] ?? '',
                'redirect_url' => $_ENV['FRONTEND_URL'] . '/payment/success'
            ];

            error_log('PaymentController::createPhonePeOrder - Creating PhonePe payment request');
            
            // ✅ Create PhonePe payment request using updated service
            $phonePeResponse = $this->phonePeService->createPaymentRequest($paymentData);

            if ($phonePeResponse && $phonePeResponse['success']) {
                error_log('PaymentController::createPhonePeOrder - PhonePe order created successfully: ' . $phonePeResponse['merchantTransactionId']);
                
                // ✅ Store transaction data in session for verification
                session_start();
                $_SESSION['phonepe_merchant_order_id'] = $phonePeResponse['merchantTransactionId'];
                $_SESSION['phonepe_amount'] = $input['amount'];
                $_SESSION['phonepe_user_id'] = $user['id'];

                Response::success([
                    'payment_url' => $phonePeResponse['paymentUrl'],
                    'merchant_transaction_id' => $phonePeResponse['merchantTransactionId'],
                    'amount' => $input['amount']
                ]);
            } else {
                $errorMessage = $phonePeResponse['error'] ?? 'Failed to create payment order';
                error_log('PaymentController::createPhonePeOrder - Failed: ' . $errorMessage);
                Response::error($errorMessage, 500);
            }

        } catch (\Exception $e) {
            error_log('PaymentController::createPhonePeOrder - Exception: ' . $e->getMessage());
            Response::error('Payment order creation failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * ✅ Verify PhonePe payment and create order
     */
    public function verifyPayment(): void
    {
        try {
            $user = AuthMiddleware::getCurrentUserData();
            if (!$user) {
                error_log('PaymentController::verifyPayment - Authentication failed');
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);
            error_log('PaymentController::verifyPayment - Input: ' . json_encode($input));

            // Validate required fields
            $merchantOrderId = $input['merchant_transaction_id'] ?? '';
            if (empty($merchantOrderId)) {
                error_log('PaymentController::verifyPayment - Missing merchant order ID');
                Response::error('Missing merchant transaction ID', 400);
                return;
            }

            // ✅ Verify payment with PhonePe using updated service
            error_log('PaymentController::verifyPayment - Verifying payment: ' . $merchantOrderId);
            $verificationResult = $this->phonePeService->verifyPayment($merchantOrderId);

            error_log('PaymentController::verifyPayment - Verification result: ' . json_encode($verificationResult));

            if (!$verificationResult || !$verificationResult['success']) {
                $errorMessage = $verificationResult['error'] ?? 'Payment verification failed';
                error_log('PaymentController::verifyPayment - Verification failed: ' . $errorMessage);
                Response::error($errorMessage, 400);
                return;
            }

            // ✅ Check payment status - working code uses 'COMPLETED'
            $paymentStatus = strtoupper($verificationResult['status'] ?? '');
            if ($paymentStatus !== 'COMPLETED') {
                error_log('PaymentController::verifyPayment - Payment not completed. Status: ' . $paymentStatus);
                Response::error('Payment not completed. Status: ' . $paymentStatus, 400);
                return;
            }

            // Get user's cart
            $cart = $this->cartModel->getUserCart($user['id']);
            if (!$cart || empty($cart['items'])) {
                error_log('PaymentController::verifyPayment - Cart is empty');
                Response::error('Cart is empty', 400);
                return;
            }

            // Create order with payment details
            $orderData = $input['order_data'] ?? [];
            $orderData['user_id'] = $user['id'];
            $orderData['customer_email'] = $user['email'];
            $orderData['customer_phone'] = $user['phone'];
            $orderData['customer_name'] = $orderData['customer_name'] ?? 
                                          (trim(($user['first_name'] ?? '') . ' ' . ($user['last_name'] ?? '')) ?: 'Customer');
            $orderData['payment_method'] = 'phonepe';
            $orderData['subtotal'] = $cart['subtotal'];
            $orderData['tax_amount'] = $cart['tax_amount'] ?? 0;
            $orderData['discount_amount'] = $cart['discount_amount'] ?? 0;
            $orderData['total_amount'] = $cart['total_amount'];
            $orderData['coupon_code'] = $cart['coupon_code'] ?? null;

            error_log('PaymentController::verifyPayment - Creating order with data: ' . json_encode($orderData));
            $orderId = $this->orderModel->createOrder($orderData, $cart['items']);

            if ($orderId) {
                error_log('PaymentController::verifyPayment - Order created successfully: ' . $orderId);
                
                // Update order with PhonePe payment details
                $this->orderModel->updatePaymentStatus($orderId, 'paid', [
                    'payment_id' => $verificationResult['orderId'],
                    'signature' => $merchantOrderId
                ]);

                // Update order status to confirmed
                $this->orderModel->updateOrderStatus($orderId, 'confirmed');

                // Clear cart
                $this->cartModel->clearCart($user['id']);

                // Get created order details
                $order = $this->orderModel->getOrderById($orderId);
                $orderItems = $this->orderModel->getOrderItems($orderId);
                $userDetails = $this->userModel->findById($user['id']);

                // Send notifications
                error_log("PaymentController::verifyPayment - Sending notifications for order: {$order['order_number']}");
                $this->sendOrderNotifications($order, $userDetails, $orderItems);

                Response::success([
                    'order_id' => $orderId,
                    'order_number' => $order['order_number'],
                    'payment_status' => 'paid',
                    'total_amount' => $order['total_amount'],
                    'message' => 'Payment successful and order created'
                ]);
            } else {
                error_log('PaymentController::verifyPayment - Failed to create order');
                Response::error('Failed to create order after payment', 500);
            }

        } catch (\Exception $e) {
            error_log('PaymentController::verifyPayment - Exception: ' . $e->getMessage());
            Response::error('Payment verification failed: ' . $e->getMessage(), 500);
        }
    }

    /**
     * ✅ Check PhonePe payment status (for polling)
     */
    public function checkPhonePePaymentStatus(array $params): void
    {
        try {
            $merchantOrderId = $params['merchantTransactionId'] ?? '';
            if (empty($merchantOrderId)) {
                Response::error('Merchant transaction ID required', 400);
                return;
            }

            error_log('PaymentController::checkPhonePePaymentStatus - Checking status for: ' . $merchantOrderId);

            // Check payment status with PhonePe
            $statusResponse = $this->phonePeService->verifyPayment($merchantOrderId);

            if ($statusResponse && $statusResponse['success']) {
                Response::success([
                    'payment_status' => strtolower($statusResponse['status'] ?? 'pending'),
                    'order_id' => $statusResponse['orderId'] ?? null,
                    'amount' => $statusResponse['amount'] ?? null,
                    'meta_info' => $statusResponse['metaInfo'] ?? null
                ]);
            } else {
                Response::error('Failed to check payment status', 500);
            }

        } catch (\Exception $e) {
            error_log('PaymentController::checkPhonePePaymentStatus - Error: ' . $e->getMessage());
            Response::error('Payment status check failed', 500);
        }
    }

    /**
     * ✅ Handle PhonePe payment callback - simplified
     */
    public function handlePhonePeCallback(): void
    {
        try {
            // Log the callback for debugging
            error_log('PhonePe Callback received: ' . json_encode($_GET) . ' POST: ' . file_get_contents('php://input'));

            // For PhonePe, the callback is mainly for logging
            // The actual verification happens when frontend calls verify endpoint
            http_response_code(200);
            echo json_encode(['success' => true, 'message' => 'Callback received']);
            exit;

        } catch (\Exception $e) {
            error_log('PhonePe Callback Error: ' . $e->getMessage());
            http_response_code(200); // Still return 200 to PhonePe
            echo json_encode(['success' => true, 'message' => 'Callback processed']);
            exit;
        }
    }

    /**
     * Handle payment failure
     */
    public function handlePaymentFailure(): void
    {
        try {
            $user = AuthMiddleware::getCurrentUserData();
            if (!$user) {
                Response::error('Authentication required', 401);
                return;
            }

            $input = json_decode(file_get_contents('php://input'), true);

            // Log payment failure
            error_log('Payment failed for user: ' . $user['id'] . ', Error: ' . json_encode($input));

            Response::success([
                'message' => 'Payment failure recorded',
                'redirect_to_cart' => true
            ]);

        } catch (\Exception $e) {
            error_log('Handle payment failure error: ' . $e->getMessage());
            Response::error('Failed to handle payment failure', 500);
        }
    }

    /**
     * Send order notifications (SMS, Email, Invoice)
     */
    private function sendOrderNotifications(array $order, array $user, array $orderItems): void
    {
        try {
            error_log("sendOrderNotifications called for order: {$order['order_number']}");

            // Only send notifications if order status is 'confirmed' and payment is 'paid'
            if ($order['status'] !== 'confirmed' || $order['payment_status'] !== 'paid') {
                error_log("Skipping notifications - order not confirmed or payment not paid");
                return;
            }

            // Send email confirmation
            $this->notificationService->sendOrderConfirmationEmail($order, $user, $orderItems);

            // Send tax invoice email
            $this->notificationService->sendTaxInvoiceEmail($order, $user, $orderItems);

            // Send direct email confirmation
            try {
                $emailSent = $this->emailService->send(
                    $user['email'],
                    "🎉 Order Confirmation - {$order['order_number']} | Wolffoxx",
                    'emails/order-confirmation',
                    [
                        'order' => $order,
                        'user' => $user,
                        'orderItems' => $orderItems
                    ]
                );

                if ($emailSent) {
                    error_log("Order confirmation email sent successfully for order {$order['order_number']}");
                }

                // Send admin copy
                $adminEmail = $_ENV['ADMIN_EMAIL'] ?? '<EMAIL>';
                if (!empty($adminEmail) && filter_var($adminEmail, FILTER_VALIDATE_EMAIL)) {
                    $this->emailService->send(
                        $adminEmail,
                        "🛎️ New Order Received - {$order['order_number']}",
                        'emails/order-confirmation',
                        [
                            'order' => $order,
                            'user' => $user,
                            'orderItems' => $orderItems,
                            'is_admin_copy' => true
                        ]
                    );
                }
            } catch (\Exception $e) {
                error_log("Direct email sending failed: " . $e->getMessage());
            }

            error_log("Order notifications sent for order: {$order['order_number']}");

        } catch (\Exception $e) {
            error_log("Failed to send order notifications: " . $e->getMessage());
        }
    }
}
